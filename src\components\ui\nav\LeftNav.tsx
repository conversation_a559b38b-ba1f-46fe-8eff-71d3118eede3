import { forwardRef } from 'react'
import type { HTMLAttributes, ReactNode } from 'react'
import { cn } from '@/lib/utils'

export interface NavItem {
  id: string
  label: string
  href?: string
  icon?: ReactNode
  onClick?: () => void
  active?: boolean
  disabled?: boolean
}

export interface LeftNavProps extends HTMLAttributes<HTMLElement> {
  children?: ReactNode
  items?: NavItem[]
  collapsed?: boolean
  onItemClick?: (item: NavItem) => void
}

const LeftNav = forwardRef<HTMLElement, LeftNavProps>(
  ({ className, children, items = [], collapsed = false, onItemClick, ...props }, ref) => {
    const handleItemClick = (item: NavItem) => {
      if (item.disabled) return
      
      if (item.onClick) {
        item.onClick()
      }
      
      if (onItemClick) {
        onItemClick(item)
      }
    }

    return (
      <nav
        ref={ref}
        className={cn(
          // Base styles - default to fixed positioning
          "fixed left-0 top-0 z-40 h-full",
          "bg-sidebar border-r border-sidebar-border",
          "flex flex-col",
          "transition-all duration-300 ease-in-out",
          // Width based on collapsed state
          collapsed ? "w-16" : "w-64",
          // When relative positioning is applied, use self-stretch to fill flex container
          className?.includes('relative') && "self-stretch",
          className
        )}
        {...props}
      >
        {/* Navigation content container */}
        <div className="flex-1 flex flex-col p-4 space-y-2">
          {/* Render navigation items if provided */}
          {items.length > 0 && (
            <ul className="space-y-1">
              {items.map((item) => (
                <li key={item.id}>
                  <button
                    onClick={() => handleItemClick(item)}
                    disabled={item.disabled}
                    className={cn(
                      // Base button styles
                      "w-full flex items-center gap-3 px-3 py-2 rounded-lg",
                      "text-left text-sm font-medium",
                      "transition-colors duration-200",
                      "focus:outline-none focus:ring-2 focus:ring-sidebar-ring",
                      // Default state
                      "text-sidebar-foreground hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",
                      // Active state
                      item.active && "bg-sidebar-primary text-sidebar-primary-foreground",
                      // Disabled state
                      item.disabled && "opacity-50 cursor-not-allowed hover:bg-transparent hover:text-sidebar-foreground",
                      // Collapsed state - center icon
                      collapsed && "justify-center px-2"
                    )}
                  >
                    {item.icon && (
                      <span className="flex-shrink-0 w-5 h-5 flex items-center justify-center">
                        {item.icon}
                      </span>
                    )}
                    {!collapsed && (
                      <span className="truncate">{item.label}</span>
                    )}
                  </button>
                </li>
              ))}
            </ul>
          )}
          
          {/* Custom children content */}
          {children}
        </div>
      </nav>
    )
  }
)

LeftNav.displayName = 'LeftNav'

export { LeftNav }
