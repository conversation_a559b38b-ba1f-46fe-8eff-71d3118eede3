import type { ReactNode } from "react"
import { Footer } from "@/components/ui/nav/Footer"
import { Header } from "@/components/ui/nav/Header"
import { LeftNav, type NavItem } from "@/components/ui/nav/LeftNav"
import { Heading3 } from "@/components/ui/typography/Heading3"

interface LayoutProps {
  children: ReactNode
  className?: string
  title?: string
  navItems?: NavItem[]
  navCollapsed?: boolean
  onNavItemClick?: (item: NavItem) => void
  showLeftNav?: boolean
}

export function Layout({
  children,
  className,
  title,
  navItems = [],
  navCollapsed = false,
  onNavItemClick,
  showLeftNav = true
}: LayoutProps) {
  return (
    <div className={`min-h-svh flex ${className || ""}`}>
      {/* Left Navigation Sidebar */}
      {showLeftNav && (
        <LeftNav
          items={navItems}
          collapsed={navCollapsed}
          onItemClick={onNavItemClick}
        />
      )}

      {/* Main Content Area */}
      <div className={`flex-1 flex flex-col ${showLeftNav ? (navCollapsed ? 'ml-16' : 'ml-64') : ''}`}>
        <Header />

        {title && <Heading3 className="text-center">{title}</Heading3>}
        <main className="flex-1 flex flex-col">
          {children}
        </main>
        <Footer />
      </div>
    </div>
  )
}
